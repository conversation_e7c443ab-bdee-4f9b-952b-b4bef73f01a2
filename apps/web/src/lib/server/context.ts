import type { NextRequest } from "next/server";
import { createServerClient } from '@supabase/ssr';
import type { User } from "@supabase/supabase-js";
import { createServerSupabaseClient } from "./supabase";

export interface UserProfile {
  id: string;
  user_id: string;
  email: string;
  full_name: string | null;
  role: 'admin' | 'user';
  created_at: string;
  updated_at: string;
}

export async function createContext(req: NextRequest) {
  let user: User | null = null;
  let userProfile: UserProfile | null = null;

  console.log("🔍 [DEBUG] createContext called for URL:", req.url);

  // Debug cookies
  const cookies = req.cookies.getAll();
  const supabaseCookies = cookies.filter(cookie =>
    cookie.name.includes('supabase') ||
    cookie.name.includes('sb-') ||
    cookie.name.includes('auth')
  );
  console.log("🔍 [DEBUG] Supabase-related cookies:", supabaseCookies.map(c => ({ name: c.name, hasValue: !!c.value })));

  try {
    // Create Supabase client for API route context with request cookies
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            const allCookies = req.cookies.getAll();
            console.log("🔍 [CONTEXT DEBUG] Request cookies:", allCookies.filter(c => c.name.includes('supabase') || c.name.includes('sb-')).map(c => ({
              name: c.name,
              hasValue: !!c.value,
              length: c.value?.length || 0
            })));
            return allCookies;
          },
          setAll(cookiesToSet) {
            // In tRPC context, we can't set cookies directly
            // This is handled by middleware
            console.log("🔍 [CONTEXT DEBUG] setAll called (ignored in tRPC context)");
          },
        },
      }
    );
    
    console.log("🔍 [CONTEXT DEBUG] Supabase client created for tRPC context");

    // First try to get the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    console.log("🔍 [CONTEXT DEBUG] getSession result:", {
      hasSession: !!session,
      hasUser: !!session?.user,
      userEmail: session?.user?.email,
      userId: session?.user?.id,
      sessionError: sessionError?.message
    });

    if (sessionError) {
      console.error("❌ [CONTEXT ERROR] Error getting session:", sessionError);
      return { user: null, userProfile: null, session: null };
    }

    if (!session?.user) {
      console.log("⚠️ [CONTEXT DEBUG] No session/user found on server");
      
      // Try getUser() as a fallback - this validates JWT token
      const { data: { user: fallbackUser }, error: userError } = await supabase.auth.getUser();
      console.log("🔍 [CONTEXT DEBUG] getUser fallback result:", {
        hasUser: !!fallbackUser,
        userEmail: fallbackUser?.email,
        userId: fallbackUser?.id,
        userError: userError?.message
      });
      
      if (fallbackUser) {
        user = fallbackUser;
        console.log("✅ [CONTEXT DEBUG] Using fallback user from getUser()");
      } else {
        console.log("❌ [CONTEXT DEBUG] No user found via getSession() or getUser()");
        return { user: null, userProfile: null, session: null };
      }
    } else {
      user = session.user;
    }

    console.log("✅ [CONTEXT DEBUG] User found:", user.email, "ID:", user.id);

    // If user is authenticated, fetch their profile with role
    if (user) {
      console.log("🔍 [CONTEXT DEBUG] Fetching user profile for user_id:", user.id);
      
      // Test auth context first
      try {
        const { data: authTest } = await supabase.rpc('get_auth_context');
        console.log("🔍 [CONTEXT DEBUG] Auth context before profile query:", authTest);
      } catch (e) {
        console.log("🔍 [CONTEXT DEBUG] Auth context test failed:", e.message);
      }

      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      console.log("🔍 [CONTEXT DEBUG] Profile query result:", {
        hasProfile: !!profile,
        profileData: profile,
        profileError: profileError?.message,
        profileErrorCode: profileError?.code,
        profileErrorDetails: profileError?.details
      });

      if (profileError) {
        console.error("❌ [CONTEXT ERROR] Error fetching user profile:", profileError);
        console.log("🔍 [CONTEXT DEBUG] Profile query was: SELECT * FROM user_profiles WHERE user_id =", user.id);
        
        // Test if JWT context is working by checking auth.uid()
        try {
          const { data: authTest } = await supabase
            .rpc('get_auth_context')
            .single();
          console.log("🔍 [CONTEXT DEBUG] Auth context test:", authTest);
        } catch (authTestError) {
          console.log("🔍 [CONTEXT DEBUG] Auth context test failed (function might not exist):", authTestError?.message);
        }
      } else if (profile) {
        userProfile = profile as UserProfile;
        console.log("✅ [CONTEXT DEBUG] User profile found:", {
          email: userProfile.email,
          role: userProfile.role,
          full_name: userProfile.full_name
        });
      } else {
        console.log("⚠️ [CONTEXT DEBUG] Profile query succeeded but returned null");
      }
    }
  } catch (error) {
    console.error("❌ [CONTEXT ERROR] Error in createContext:", error);
    // Fail silently to not break non-auth operations
  }

  console.log("🔍 [DEBUG] createContext returning:", {
    hasUser: !!user,
    hasUserProfile: !!userProfile,
    userEmail: user?.email,
    userRole: userProfile?.role
  });

  return {
    user,
    userProfile,
    session: user ? { user } : null,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;