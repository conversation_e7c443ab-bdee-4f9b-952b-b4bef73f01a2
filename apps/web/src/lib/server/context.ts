import type { NextRequest } from "next/server";
import { createServerSupabaseClient } from "./supabase";
import type { User } from "@supabase/supabase-js";

export interface UserProfile {
  id: string;
  user_id: string;
  email: string;
  full_name: string | null;
  role: 'admin' | 'user';
  created_at: string;
  updated_at: string;
}

export async function createContext(req: NextRequest) {
  let user: User | null = null;
  let userProfile: UserProfile | null = null;

  console.log("🔍 [DEBUG] createContext called");

  try {
    const supabase = await createServerSupabaseClient();
    console.log("🔍 [DEBUG] Supabase client created");

    // First try to get the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error("❌ [ERROR] Error getting session:", sessionError);
      return { user: null, userProfile: null, session: null };
    }

    if (!session) {
      console.log("⚠️ [DEBUG] No session found");
      return { user: null, userProfile: null, session: null };
    }

    console.log("✅ [DEBUG] Session found for user:", session.user.email, "ID:", session.user.id);

    // Use the user from the session
    user = session.user;

    // If user is authenticated, fetch their profile with role
    if (user) {
      console.log("🔍 [DEBUG] Fetching user profile for user_id:", user.id);

      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (profileError) {
        console.error("❌ [ERROR] Error fetching user profile:", profileError);
        console.log("🔍 [DEBUG] Query was: SELECT * FROM user_profiles WHERE user_id =", user.id);
      } else {
        userProfile = profile as UserProfile;
        console.log("✅ [DEBUG] User profile found:", {
          email: userProfile.email,
          role: userProfile.role,
          full_name: userProfile.full_name
        });
      }
    }
  } catch (error) {
    console.error("❌ [ERROR] Error in createContext:", error);
    // Fail silently to not break non-auth operations
  }

  console.log("🔍 [DEBUG] createContext returning:", {
    hasUser: !!user,
    hasUserProfile: !!userProfile,
    userEmail: user?.email,
    userRole: userProfile?.role
  });

  return {
    user,
    userProfile,
    session: user ? { user } : null,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;