import type { NextRequest } from "next/server";
import { createServerSupabaseClient } from "./supabase";
import type { User } from "@supabase/supabase-js";

export interface UserProfile {
  id: string;
  user_id: string;
  email: string;
  full_name: string | null;
  role: 'admin' | 'user';
  created_at: string;
  updated_at: string;
}

export async function createContext(req: NextRequest) {
  let user: User | null = null;
  let userProfile: UserProfile | null = null;
  
  try {
    const supabase = await createServerSupabaseClient();
    
    // First try to get the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error("Error getting session:", sessionError);
      return { user: null, userProfile: null, session: null };
    }
    
    if (!session) {
      return { user: null, userProfile: null, session: null };
    }
    
    // Use the user from the session
    user = session.user;
    
    // If user is authenticated, fetch their profile with role
    if (user) {
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();
      
      if (profileError) {
        console.error("Error fetching user profile:", profileError);
      } else {
        userProfile = profile as UserProfile;
      }
    }
  } catch (error) {
    console.error("Error in createContext:", error);
    // Fail silently to not break non-auth operations
  }
  
  return {
    user,
    userProfile,
    session: user ? { user } : null,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;