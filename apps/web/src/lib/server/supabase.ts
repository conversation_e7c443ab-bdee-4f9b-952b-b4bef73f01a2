import { createClient } from '@supabase/supabase-js';
import { createServerClient } from '@supabase/ssr';
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables');
}

// Basic client for non-auth operations
export const supabase = createClient(supabaseUrl, supabaseKey);

// Server client for auth operations in API routes
export async function createServerSupabaseClient() {
  const cookieStore = await cookies();
  
  return createServerClient(supabaseUrl, supabaseKey, {
    cookies: {
      getAll() {
        return cookieStore.getAll();
      },
      setAll(cookiesToSet) {
        try {
          cookiesToSet.forEach(({ name, value, options }) => {
            const cookieOptions = {
              ...options,
              httpOnly: true,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'lax' as const,
              path: '/',
              maxAge: options?.maxAge || 60 * 60 * 24 * 30, // 30 days
            };
            cookieStore.set(name, value, cookieOptions);
          });
        } catch (error) {
          // The `setAll` method was called from a Server Component.
          // This can be ignored if you have middleware refreshing
          // user sessions.
          console.error('Error setting cookies:', error);
        }
      },
    },
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
  });
}

// Middleware client for session refresh
export function createMiddlewareSupabaseClient(
  request: NextRequest,
  response: NextResponse
) {
  return createServerClient(supabaseUrl, supabaseKey, {
    cookies: {
      getAll() {
        return request.cookies.getAll();
      },
      setAll(cookiesToSet) {
        cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value));
        cookiesToSet.forEach(({ name, value, options }) =>
          response.cookies.set(name, value, options)
        );
      },
    },
  });
}

// Types for our content pieces
export interface ContentPiece {
  id: number;
  content_uuid: string;
  content_link: string;
  content_tags: string[];
  content_account: string;
  content_created_date: string;
  content_types: string[];
  twitter_content_type: string | null;
  twitter_impressions: number;
  twitter_likes: number;
  twitter_retweets: number;
  content_title: string | null;
  content_description: string | null;
  content_categories: string[];
  created_at: string;
  updated_at: string;
}

export interface SearchResult extends ContentPiece {
  rank: number;
}

// Types for groups feature
export interface Group {
  id: string;
  name: string;
  user_id: string;
  description: string | null;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export interface GroupContent {
  id: number;
  group_id: string;
  content_id: number;
  position: number;
  added_at: string;
}

export interface GroupWithContent extends Group {
  content: (ContentPiece & { position: number; added_at: string })[];
  content_count: number;
}