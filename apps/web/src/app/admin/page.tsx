"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { useAuth } from "@/components/auth/auth-provider";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loader from "@/components/loader";
import { toast } from "sonner";
import { Plus, Edit, Trash2, Shield, BarChart3 } from "lucide-react";

interface ContentFormData {
  content_uuid: string;
  content_link: string;
  content_tags: string[];
  content_account: string;
  content_created_date: string;
  content_types: string[];
  twitter_content_type: string | null;
  twitter_impressions: number;
  twitter_likes: number;
  twitter_retweets: number;
  content_title: string | null;
  content_description: string | null;
  content_categories: string[];
}

const initialFormData: ContentFormData = {
  content_uuid: "",
  content_link: "",
  content_tags: [],
  content_account: "",
  content_created_date: new Date().toISOString().split('T')[0],
  content_types: [],
  twitter_content_type: null,
  twitter_impressions: 0,
  twitter_likes: 0,
  twitter_retweets: 0,
  content_title: null,
  content_description: null,
  content_categories: [],
};

export default function AdminPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [formData, setFormData] = useState<ContentFormData>(initialFormData);
  const [showForm, setShowForm] = useState(false);

  // Check if user is admin
  const userProfileQuery = trpc.getUserProfile.useQuery(undefined, {
    enabled: !!user,
    retry: false,
  });

  // Debug logging
  console.log("🔍 [ADMIN PAGE DEBUG] Auth state:", {
    hasUser: !!user,
    userEmail: user?.email,
    authLoading,
    profileLoading: userProfileQuery.isLoading,
    profileError: userProfileQuery.error,
    profileData: userProfileQuery.data,
    userRole: userProfileQuery.data?.role
  });

  // Get content for admin management
  const contentQuery = trpc.getAllContent.useQuery({
    page: 0,
    limit: 50,
  });

  // Mutations
  const createContentMutation = trpc.createContent.useMutation({
    onSuccess: () => {
      toast.success("Content created successfully!");
      setFormData(initialFormData);
      setShowForm(false);
      contentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to create content: ${error.message}`);
    },
  });

  const deleteContentMutation = trpc.deleteContent.useMutation({
    onSuccess: () => {
      toast.success("Content deleted successfully!");
      contentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete content: ${error.message}`);
    },
  });

  const updateStatsMutation = trpc.updateStats.useMutation({
    onSuccess: (data) => {
      toast.success(`Stats updated successfully! ${data.stats.totalImpressions} impressions, ${data.stats.totalContent} content pieces`);
    },
    onError: (error) => {
      toast.error(`Failed to update stats: ${error.message}`);
    },
  });

  // Loading states
  if (authLoading || userProfileQuery.isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader />
      </div>
    );
  }

  // Not authenticated
  if (!user) {
    router.push("/");
    return null;
  }

  // Not admin
  if (!userProfileQuery.data || userProfileQuery.data.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 max-w-md text-center">
          <Shield className="mx-auto mb-4 h-12 w-12 text-red-500" />
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-muted-foreground mb-4">
            You don't have permission to access this page.
          </p>
          <Button onClick={() => router.push("/")} variant="outline">
            Go Home
          </Button>
        </Card>
      </div>
    );
  }

  const handleInputChange = (field: keyof ContentFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayInputChange = (field: keyof ContentFormData, value: string) => {
    const array = value.split(',').map(item => item.trim()).filter(Boolean);
    setFormData(prev => ({
      ...prev,
      [field]: array
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createContentMutation.mutate(formData);
  };

  const handleDelete = (id: number) => {
    if (window.confirm("Are you sure you want to delete this content?")) {
      deleteContentMutation.mutate({ id });
    }
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Admin Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back, {userProfileQuery.data?.full_name || userProfileQuery.data?.email}
            </p>
          </div>
          <div className="flex gap-2">
            <Button 
              onClick={() => updateStatsMutation.mutate()}
              variant="outline"
              className="flex items-center gap-2"
              disabled={updateStatsMutation.isPending}
            >
              <BarChart3 className="h-4 w-4" />
              {updateStatsMutation.isPending ? "Updating..." : "Update Stats"}
            </Button>
            <Button 
              onClick={() => setShowForm(!showForm)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Content
            </Button>
          </div>
        </div>

        {/* Create Content Form */}
        {showForm && (
          <Card className="p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Create New Content</h2>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="content_uuid">Content UUID</Label>
                  <Input
                    id="content_uuid"
                    value={formData.content_uuid}
                    onChange={(e) => handleInputChange('content_uuid', e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="content_link">Content Link</Label>
                  <Input
                    id="content_link"
                    type="url"
                    value={formData.content_link}
                    onChange={(e) => handleInputChange('content_link', e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="content_account">Content Account</Label>
                  <Input
                    id="content_account"
                    value={formData.content_account}
                    onChange={(e) => handleInputChange('content_account', e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="content_created_date">Created Date</Label>
                  <Input
                    id="content_created_date"
                    type="date"
                    value={formData.content_created_date}
                    onChange={(e) => handleInputChange('content_created_date', e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="content_title">Title (optional)</Label>
                  <Input
                    id="content_title"
                    value={formData.content_title || ''}
                    onChange={(e) => handleInputChange('content_title', e.target.value || null)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="twitter_content_type">Twitter Content Type</Label>
                  <Input
                    id="twitter_content_type"
                    value={formData.twitter_content_type || ''}
                    onChange={(e) => handleInputChange('twitter_content_type', e.target.value || null)}
                    placeholder="tweet, space, etc."
                  />
                </div>
                
                <div>
                  <Label htmlFor="twitter_impressions">Twitter Impressions</Label>
                  <Input
                    id="twitter_impressions"
                    type="number"
                    value={formData.twitter_impressions}
                    onChange={(e) => handleInputChange('twitter_impressions', parseInt(e.target.value) || 0)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="twitter_likes">Twitter Likes</Label>
                  <Input
                    id="twitter_likes"
                    type="number"
                    value={formData.twitter_likes}
                    onChange={(e) => handleInputChange('twitter_likes', parseInt(e.target.value) || 0)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="twitter_retweets">Twitter Retweets</Label>
                  <Input
                    id="twitter_retweets"
                    type="number"
                    value={formData.twitter_retweets}
                    onChange={(e) => handleInputChange('twitter_retweets', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="content_description">Description (optional)</Label>
                <Input
                  id="content_description"
                  value={formData.content_description || ''}
                  onChange={(e) => handleInputChange('content_description', e.target.value || null)}
                />
              </div>
              
              <div>
                <Label htmlFor="content_tags">Tags (comma-separated)</Label>
                <Input
                  id="content_tags"
                  value={formData.content_tags.join(', ')}
                  onChange={(e) => handleArrayInputChange('content_tags', e.target.value)}
                  placeholder="tag1, tag2, tag3"
                />
              </div>
              
              <div>
                <Label htmlFor="content_types">Content Types (comma-separated)</Label>
                <Input
                  id="content_types"
                  value={formData.content_types.join(', ')}
                  onChange={(e) => handleArrayInputChange('content_types', e.target.value)}
                  placeholder="twitter, marketing, etc."
                />
              </div>
              
              <div>
                <Label htmlFor="content_categories">Categories (comma-separated)</Label>
                <Input
                  id="content_categories"
                  value={formData.content_categories.join(', ')}
                  onChange={(e) => handleArrayInputChange('content_categories', e.target.value)}
                  placeholder="category1, category2, etc."
                />
              </div>
              
              <div className="flex gap-2 pt-4">
                <Button 
                  type="submit" 
                  disabled={createContentMutation.isPending}
                >
                  {createContentMutation.isPending ? "Creating..." : "Create Content"}
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </Card>
        )}

        {/* Content List */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Manage Content</h2>
          
          {contentQuery.isLoading ? (
            <div className="flex justify-center py-8">
              <Loader />
            </div>
          ) : (
            <div className="space-y-4">
              {contentQuery.data?.map((content) => (
                <div
                  key={content.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex-1">
                    <h3 className="font-medium">
                      {content.content_title || content.content_account}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {content.content_link}
                    </p>
                    <div className="flex gap-4 text-xs text-muted-foreground mt-1">
                      <span>👁 {content.twitter_impressions}</span>
                      <span>❤️ {content.twitter_likes}</span>
                      <span>🔄 {content.twitter_retweets}</span>
                      <span>📅 {content.content_created_date}</span>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        // TODO: Implement edit functionality
                        toast.info("Edit functionality coming soon!");
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDelete(content.id)}
                      disabled={deleteContentMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
              
              {contentQuery.data?.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No content found. Create your first entry!
                </div>
              )}
            </div>
          )}
        </Card>
      </div>
    </div>
  );
}