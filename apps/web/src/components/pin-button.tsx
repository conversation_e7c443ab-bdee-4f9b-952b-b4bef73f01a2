"use client";

import { useState } from "react";
import { trpc } from "@/utils/trpc";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Pin, PinOff, Plus, Check, Folder } from "lucide-react";
import { toast } from "sonner";

interface PinButtonProps {
  contentId: number;
  contentTitle?: string | null;
  size?: "sm" | "default" | "lg";
  variant?: "default" | "outline" | "ghost";
}

export function PinButton({ 
  contentId, 
  contentTitle, 
  size = "sm", 
  variant = "outline" 
}: PinButtonProps) {
  const [showCreateGroup, setShowCreateGroup] = useState(false);
  const [newGroupName, setNewGroupName] = useState("");

  const utils = trpc.useUtils();
  
  // Get user's groups
  const { data: groups } = trpc.getGroups.useQuery({});
  
  // Get current pin status for this content
  const { data: groupStatus } = trpc.getContentGroupStatus.useQuery({ 
    content_id: contentId 
  });
  
  const isPinned = groupStatus && groupStatus.length > 0;

  // Mutations
  const addToGroupMutation = trpc.addContentToGroup.useMutation({
    onSuccess: () => {
      toast.success("Content pinned to group!");
      utils.getContentGroupStatus.invalidate({ content_id: contentId });
      utils.getGroups.invalidate();
    },
    onError: (error) => {
      if (error.message.includes("duplicate key")) {
        toast.error("Content is already in this group");
      } else {
        toast.error(error.message);
      }
    },
  });

  const removeFromGroupMutation = trpc.removeContentFromGroup.useMutation({
    onSuccess: () => {
      toast.success("Content unpinned from group!");
      utils.getContentGroupStatus.invalidate({ content_id: contentId });
      utils.getGroups.invalidate();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const createGroupMutation = trpc.createGroup.useMutation({
    onSuccess: (newGroup) => {
      // Add content to the newly created group
      addToGroupMutation.mutate({
        group_id: newGroup.id,
        content_id: contentId,
      });
      setShowCreateGroup(false);
      setNewGroupName("");
      utils.getGroups.invalidate();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const handleAddToGroup = (groupId: string) => {
    addToGroupMutation.mutate({
      group_id: groupId,
      content_id: contentId,
    });
  };

  const handleRemoveFromGroup = (groupId: string) => {
    removeFromGroupMutation.mutate({
      group_id: groupId,
      content_id: contentId,
    });
  };

  const handleCreateGroup = () => {
    if (!newGroupName.trim()) {
      toast.error("Group name is required");
      return;
    }

    createGroupMutation.mutate({
      name: newGroupName.trim(),
    });
  };

  // Quick create group dialog
  if (showCreateGroup) {
    return (
      <Card className="absolute top-0 left-0 z-50 w-80 shadow-lg">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Create New Group</CardTitle>
          <CardDescription className="text-xs">
            Pin "{contentTitle || "this content"}" to a new group
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <Label htmlFor="group-name" className="text-xs">Group Name</Label>
            <Input
              id="group-name"
              placeholder="Enter group name"
              value={newGroupName}
              onChange={(e) => setNewGroupName(e.target.value)}
              maxLength={100}
              className="h-8 text-sm"
              autoFocus
            />
          </div>
          
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={handleCreateGroup}
              disabled={createGroupMutation.isPending}
              className="h-7 text-xs"
            >
              {createGroupMutation.isPending ? "Creating..." : "Create & Pin"}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setShowCreateGroup(false);
                setNewGroupName("");
              }}
              className="h-7 text-xs"
            >
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={isPinned ? "default" : variant}
          size={size}
          className={`relative ${isPinned ? "bg-blue-600 hover:bg-blue-700" : ""}`}
        >
          {isPinned ? (
            <>
              <Pin className="h-3 w-3 mr-1" />
              {groupStatus?.length === 1 ? "Pinned" : `${groupStatus?.length} Groups`}
            </>
          ) : (
            <>
              <PinOff className="h-3 w-3 mr-1" />
              Pin
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-56">
        {groups && groups.length > 0 ? (
          <>
            <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground">
              Pin to group:
            </div>
            {groups.map((group) => {
              const isInGroup = groupStatus?.some(gs => gs.group_id === group.id);
              return (
                <DropdownMenuItem
                  key={group.id}
                  onClick={() => {
                    if (isInGroup) {
                      handleRemoveFromGroup(group.id);
                    } else {
                      handleAddToGroup(group.id);
                    }
                  }}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2">
                    <Folder className="h-3 w-3" />
                    <span className="truncate">{group.name}</span>
                  </div>
                  {isInGroup && <Check className="h-3 w-3 text-green-600" />}
                </DropdownMenuItem>
              );
            })}
            <DropdownMenuSeparator />
          </>
        ) : (
          <div className="px-2 py-2 text-xs text-muted-foreground">
            No groups yet
          </div>
        )}
        
        <DropdownMenuItem
          onClick={() => setShowCreateGroup(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-3 w-3" />
          <span>Create New Group</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}